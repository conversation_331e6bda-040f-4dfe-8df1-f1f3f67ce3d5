import logging
from typing import Dict, Any, Optional

from controller.validation import validate_hs_code, validate_country, validate_months, sanitize_input
from service.country_service import CountryService
from service.ranking_service import RankingService

logger = logging.getLogger(__name__)

class CountryController:
    """
    Controller for country-related operations.
    """

    def __init__(self, country_service: CountryService, ranking_service: RankingService):
        """
        Initialize the country controller.

        Args:
            country_service: Service for country operations
            ranking_service: Service for ranking operations
        """
        self.country_service = country_service
        self.ranking_service = ranking_service

    def get_top_countries(self, hs_code: str, chemical_name: str, destination: str, months: int = 12) -> Dict[str, Any]:
        """
        Get top countries exporting a product with given HS code.

        Args:
            hs_code: The HS code to search for
            chemical_name: The name of the chemical
            destination: The destination country
            months: Number of months of data to retrieve

        Returns:
            Dictionary with status and data fields
        """
        # Validate input
        is_valid, error_message = validate_hs_code(hs_code)
        if not is_valid:
            return {
                "status": "error",
                "message": error_message
            }

        is_valid, error_message = validate_country(destination)
        if not is_valid:
            return {
                "status": "error",
                "message": error_message
            }

        is_valid, error_message = validate_months(months)
        if not is_valid:
            return {
                "status": "error",
                "message": error_message
            }

        # Sanitize inputs
        hs_code = sanitize_input(hs_code)
        chemical_name = sanitize_input(chemical_name) if chemical_name else None
        destination = sanitize_input(destination)

        # Log the request
        logger.info(f"Getting top countries for HS code: {hs_code}, destination: {destination}, months: {months}")

        try:
            # Call the service to get top countries
            result = self.country_service.get_top_countries(hs_code, chemical_name, destination, months)

            # Format the response
            if result.status == "success" and result.data:
                return {
                    "status": "success",
                    "data": result.data
                }
            elif result.status == "warning":
                return {
                    "status": "warning",
                    "message": result.message,
                    "data": result.data or []
                }
            else:
                return {
                    "status": "error",
                    "message": result.message or "No countries found for the given HS code"
                }

        except Exception as e:
            logger.error(f"Error getting top countries: {str(e)}")
            return {
                "status": "error",
                "message": f"Error getting top countries: {str(e)}"
            }

    def get_top_countries_with_tariff(self, hs_code: str, chemical_name: str, destination: str, months: int = 12) -> Dict[str, Any]:
        """
        Get top countries exporting a product with given HS code, including tariff information.

        Args:
            hs_code: The HS code to search for
            chemical_name: The name of the chemical
            destination: The destination country
            months: Number of months of data to retrieve

        Returns:
            Dictionary with status and data fields
        """
        # Validate input
        is_valid, error_message = validate_hs_code(hs_code)
        if not is_valid:
            return {
                "status": "error",
                "message": error_message
            }

        is_valid, error_message = validate_country(destination)
        if not is_valid:
            return {
                "status": "error",
                "message": error_message
            }

        is_valid, error_message = validate_months(months)
        if not is_valid:
            return {
                "status": "error",
                "message": error_message
            }

        # Sanitize inputs
        hs_code = sanitize_input(hs_code)
        chemical_name = sanitize_input(chemical_name) if chemical_name else None
        destination = sanitize_input(destination)

        # Log the request
        logger.info(f"Getting top countries with tariff for HS code: {hs_code}, destination: {destination}, months: {months}")

        try:
            # Call the service to get top countries with tariff
            result = self.country_service.get_top_countries_with_tariff(hs_code, chemical_name, destination, months)

            # Format the response
            if result.status == "success" and result.data:
                return {
                    "status": "success",
                    "data": result.data
                }
            elif result.status == "warning":
                return {
                    "status": "warning",
                    "message": result.message,
                    "data": result.data or []
                }
            else:
                return {
                    "status": "error",
                    "message": result.message or "No countries found for the given HS code"
                }

        except Exception as e:
            logger.error(f"Error getting top countries with tariff: {str(e)}")
            return {
                "status": "error",
                "message": f"Error getting top countries with tariff: {str(e)}"
            }

    def rank_countries(self, countries_data: list, top_n: int = 3) -> Dict[str, Any]:
        """
        Rank countries based on composite scoring.

        Args:
            countries_data: List of country dictionaries
            top_n: Number of top countries to return

        Returns:
            Dictionary with status and data fields
        """
        if not countries_data:
            return {
                "status": "error",
                "message": "No countries provided for ranking"
            }

        try:
            # Call the ranking service to rank countries
            ranked_countries = self.ranking_service.rank_countries(countries_data, top_n)

            if ranked_countries:
                return {
                    "status": "success",
                    "data": ranked_countries
                }
            else:
                return {
                    "status": "warning",
                    "message": "No countries could be ranked",
                    "data": []
                }

        except Exception as e:
            logger.error(f"Error ranking countries: {str(e)}")
            return {
                "status": "error",
                "message": f"Error ranking countries: {str(e)}"
            }
