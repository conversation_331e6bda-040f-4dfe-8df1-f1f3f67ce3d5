import logging
from typing import Dict, Any, Optional

from controller.validation import validate_hs_code, validate_country, validate_months, sanitize_input
from service.supplier_service import SupplierService
from service.ranking_service import RankingService

logger = logging.getLogger(__name__)

class SupplierController:
    """
    Controller for supplier-related operations.
    """

    def __init__(self, supplier_service: SupplierService, ranking_service: RankingService):
        """
        Initialize the supplier controller.

        Args:
            supplier_service: Service for supplier operations
            ranking_service: Service for ranking operations
        """
        self.supplier_service = supplier_service
        self.ranking_service = ranking_service

    def get_top_suppliers(self, hs_code: str, country: str, destination: str, chemical_name: str = None, months: int = 12) -> Dict[str, Any]:
        """
        Get top suppliers from a specific country for a given HS code.

        Args:
            hs_code: The HS code to search for
            country: The origin country to search for suppliers
            destination: The destination country
            chemical_name: The name of the chemical
            months: Number of months of data to retrieve

        Returns:
            Dictionary with status and data fields
        """
        # Validate input
        is_valid, error_message = validate_hs_code(hs_code)
        if not is_valid:
            return {
                "status": "error",
                "message": error_message
            }

        is_valid, error_message = validate_country(country)
        if not is_valid:
            return {
                "status": "error",
                "message": error_message
            }

        is_valid, error_message = validate_country(destination)
        if not is_valid:
            return {
                "status": "error",
                "message": error_message
            }

        is_valid, error_message = validate_months(months)
        if not is_valid:
            return {
                "status": "error",
                "message": error_message
            }

        # Sanitize inputs
        hs_code = sanitize_input(hs_code)
        country = sanitize_input(country)
        destination = sanitize_input(destination)
        chemical_name = sanitize_input(chemical_name) if chemical_name else None

        # Log the request
        logger.info(f"Getting top suppliers for HS code: {hs_code}, country: {country}, destination: {destination}, months: {months}")

        try:
            # Call the service to get top suppliers
            result = self.supplier_service.get_top_suppliers(hs_code, country, destination, chemical_name, months)

            # Format the response
            if result.status == "success" and result.data:
                return {
                    "status": "success",
                    "data": result.data
                }
            elif result.status == "warning":
                return {
                    "status": "warning",
                    "message": result.message,
                    "data": result.data or []
                }
            else:
                return {
                    "status": "error",
                    "message": result.message or f"No suppliers found in {country} for the given HS code"
                }

        except Exception as e:
            logger.error(f"Error getting top suppliers: {str(e)}")
            return {
                "status": "error",
                "message": f"Error getting top suppliers: {str(e)}"
            }

    def get_supplier_details(self, supplier_name: str, hs_code: str, chemical_name: str, country: str) -> Dict[str, Any]:
        """
        Get detailed information about a supplier.

        Args:
            supplier_name: The name of the supplier
            hs_code: The HS code of the product
            chemical_name: The name of the chemical
            country: The country of the supplier

        Returns:
            Dictionary with status and data fields
        """
        # Validate input
        is_valid, error_message = validate_hs_code(hs_code)
        if not is_valid:
            return {
                "status": "error",
                "message": error_message
            }

        is_valid, error_message = validate_country(country)
        if not is_valid:
            return {
                "status": "error",
                "message": error_message
            }

        # Sanitize inputs
        supplier_name = sanitize_input(supplier_name)
        hs_code = sanitize_input(hs_code)
        chemical_name = sanitize_input(chemical_name) if chemical_name else None
        country = sanitize_input(country)

        # Log the request
        logger.info(f"Getting details for supplier: {supplier_name}, country: {country}, HS code: {hs_code}")

        try:
            # Call the service to get supplier details
            result = self.supplier_service.get_supplier_details(supplier_name, hs_code, chemical_name, country)

            if result:
                return {
                    "status": "success",
                    "data": result
                }
            else:
                return {
                    "status": "error",
                    "message": f"No details found for supplier {supplier_name}"
                }

        except Exception as e:
            logger.error(f"Error getting supplier details: {str(e)}")
            return {
                "status": "error",
                "message": f"Error getting supplier details: {str(e)}"
            }

    def rank_suppliers(self, suppliers_data: list, top_n: int = 3) -> Dict[str, Any]:
        """
        Rank suppliers based on composite scoring.

        Args:
            suppliers_data: List of supplier dictionaries
            top_n: Number of top suppliers to return

        Returns:
            Dictionary with status and data fields
        """
        if not suppliers_data:
            return {
                "status": "error",
                "message": "No suppliers provided for ranking"
            }

        try:
            # Call the ranking service to rank suppliers
            ranked_suppliers = self.ranking_service.rank_suppliers(suppliers_data, top_n)

            if ranked_suppliers:
                return {
                    "status": "success",
                    "data": ranked_suppliers
                }
            else:
                return {
                    "status": "warning",
                    "message": "No suppliers could be ranked",
                    "data": []
                }

        except Exception as e:
            logger.error(f"Error ranking suppliers: {str(e)}")
            return {
                "status": "error",
                "message": f"Error ranking suppliers: {str(e)}"
            }
