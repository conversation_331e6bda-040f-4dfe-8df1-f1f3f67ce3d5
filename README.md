# Procure Intelligence

A comprehensive chemical procurement intelligence platform that helps users find suppliers for chemicals based on HS codes, trade data, and supplier information.

## Overview

This module integrates the functionality of four separate components into a single, structured application:

1. **name2hs_resolver**: Resolves chemical names to HS codes
2. **volza_query_engine**: Queries trade data for suppliers and countries
3. **supplier_intel**: Ranks suppliers and countries based on various metrics
4. **procure_intelligence_webapp**: Frontend web application

## Features

- Chemical name to HS code resolution
- Country-level trade data analysis
- Supplier identification and ranking
- Tariff information integration
- LLM-powered data enrichment when database data is insufficient

## Project Structure

```
procure_intelligence/
├── controller/           # Input validation, data matching, and sanity checks
│   ├── chemical_controller.py
│   ├── country_controller.py
│   ├── supplier_controller.py
│   └── validation.py
├── service/              # Business logic
│   ├── chemical_service.py
│   ├── country_service.py
│   ├── ranking_service.py
│   ├── supplier_service.py
│   └── client/           # External systems like LLMs, APIs, etc.
│       ├── db_client.py
│       ├── llm_client.py
│       ├── openai_client.py
│       └── perplexity_client.py
├── model/                # Data models
│   ├── chemical.py
│   ├── country.py
│   └── supplier.py
├── data/                 # Data files
│   ├── chemical_name_variations.json
│   └── historical_data.csv
├── utils/                # Utility functions
│   └── formatters.py
├── app.py                # Main application
└── requirements.txt      # Dependencies
```

## Setup

1. Create a virtual environment:
```bash
python3 -m venv venv
source venv/bin/activate
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. Set up environment variables:
Create a `.env` file with the following variables:
```
PERPLEXITY_API_KEY=your_perplexity_api_key
OPENAI_API_KEY=your_openai_api_key
DB_HOST=your_db_host
DB_PORT=your_db_port
DB_NAME=your_db_name
DB_USER=your_db_user
DB_PASSWORD=your_db_password
```

4. Run the application:
```bash
streamlit run app.py
```

## API Endpoints

The application doesn't expose direct API endpoints, but the controllers provide the following functionality:

### Chemical Controller
- `get_hs_codes(chemical_name, application, category)`: Get HS codes for a chemical

### Country Controller
- `get_top_countries(hs_code, chemical_name, destination, months)`: Get top countries exporting a product
- `get_top_countries_with_tariff(hs_code, chemical_name, destination, months)`: Get top countries with tariff information
- `rank_countries(countries_data, top_n)`: Rank countries based on composite scoring

### Supplier Controller
- `get_top_suppliers(hs_code, country, destination, chemical_name, months)`: Get top suppliers from a specific country
- `get_supplier_details(supplier_name, hs_code, chemical_name, country)`: Get detailed information about a supplier
- `rank_suppliers(suppliers_data, top_n)`: Rank suppliers based on composite scoring

## Dependencies

- streamlit: Web application framework
- pandas: Data manipulation
- plotly: Data visualization
- requests: HTTP requests
- openai: OpenAI API client
- psycopg2-binary: PostgreSQL database client
- python-dotenv: Environment variable management
- beautifulsoup4: HTML parsing

## License

This project is proprietary and confidential.

## Credits

This project integrates code and functionality from:
- name2hs_resolver
- volza_query_engine
- supplier_intel
- procure_intelligence_webapp
