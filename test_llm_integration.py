#!/usr/bin/env python3
"""
Test script to verify LLM integration is working properly.
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add the current directory to Python path
sys.path.append('.')

from service.client.llm_client import LLMClient
from service.client.db_client import DatabaseClient
from service.country_service import CountryService

def test_llm_integration():
    """Test the LLM integration for country data generation."""
    print("Testing LLM Integration...")
    print("=" * 50)

    # Get API keys
    perplexity_api_key = os.getenv("PERPLEXITY_API_KEY")
    openai_api_key = os.getenv("OPENAI_API_KEY")

    print(f"Perplexity API Key: {'✓ Found' if perplexity_api_key else '✗ Missing'}")
    print(f"OpenAI API Key: {'✓ Found' if openai_api_key else '✗ Missing'}")
    print()

    # Initialize LLM client
    llm_client = LLMClient(perplexity_api_key, openai_api_key)
    print(f"LLM Client initialized: ✓")
    print(f"Perplexity client available: {'✓' if llm_client.perplexity_client else '✗'}")
    print(f"OpenAI client available: {'✓' if llm_client.openai_client else '✗'}")
    print()

    # Initialize database client and country service
    db_config = {
        'host': os.getenv('DB_HOST', 'localhost'),
        'port': os.getenv('DB_PORT', 5432),
        'database': os.getenv('DB_NAME', 'volza_trade_data'),
        'user': os.getenv('DB_USER', 'postgres'),
        'password': os.getenv('DB_PASSWORD', 'postgres')
    }
    db_client = DatabaseClient(db_config)
    country_service = CountryService(db_client, llm_client)
    print(f"Country Service initialized with LLM client: ✓")
    print()

    # Test country data generation
    print("Testing country data generation...")
    test_hs_code = "2915.90"
    test_chemical = "Acetic Acid"
    test_destination = "United States"

    print(f"Test parameters:")
    print(f"  HS Code: {test_hs_code}")
    print(f"  Chemical: {test_chemical}")
    print(f"  Destination: {test_destination}")
    print()

    # Test LLM client directly
    print("1. Testing LLM client directly...")
    try:
        result = llm_client.generate_country_data(test_hs_code, test_chemical, test_destination, False)
        print(f"   Status: {result['status']}")
        print(f"   Message: {result['message']}")
        print(f"   Data count: {len(result['data']) if result['data'] else 0}")
        if result['data']:
            print(f"   First country: {result['data'][0].get('country', 'Unknown')}")
            print(f"   From LLM: {result['data'][0].get('from_llm', False)}")
        print("   ✓ LLM client test passed")
    except Exception as e:
        print(f"   ✗ LLM client test failed: {str(e)}")
    print()

    # Test country service
    print("2. Testing country service...")
    try:
        result = country_service._generate_country_data(test_hs_code, test_chemical, test_destination, False)
        print(f"   Status: {result['status']}")
        print(f"   Message: {result['message']}")
        print(f"   Data count: {len(result['data']) if result['data'] else 0}")
        if result['data']:
            print(f"   First country: {result['data'][0].get('country', 'Unknown')}")
            print(f"   From LLM: {result['data'][0].get('from_llm', False)}")
        print("   ✓ Country service test passed")
    except Exception as e:
        print(f"   ✗ Country service test failed: {str(e)}")
    print()

    # Test with tariff data
    print("3. Testing with tariff data...")
    try:
        result = llm_client.generate_country_data(test_hs_code, test_chemical, test_destination, True)
        print(f"   Status: {result['status']}")
        print(f"   Message: {result['message']}")
        print(f"   Data count: {len(result['data']) if result['data'] else 0}")
        if result['data']:
            print(f"   First country: {result['data'][0].get('country', 'Unknown')}")
            print(f"   Has tariff data: {'duty' in result['data'][0]}")
        print("   ✓ Tariff data test passed")
    except Exception as e:
        print(f"   ✗ Tariff data test failed: {str(e)}")
    print()

    print("=" * 50)
    print("Test completed!")

if __name__ == "__main__":
    test_llm_integration()
