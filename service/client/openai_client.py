import json
import logging
from typing import Dict, Any, Optional
from openai import OpenAI

logger = logging.getLogger(__name__)

class OpenAIClient:
    """
    Client for interacting with the OpenAI API.
    """
    
    def __init__(self, api_key: str):
        """
        Initialize the OpenAI client with an API key.
        
        Args:
            api_key: OpenAI API key
        """
        self.api_key = api_key
        self.client = None
        self.default_model = "gpt-4o"
        
        if api_key:
            try:
                self.client = OpenAI(api_key=api_key)
            except Exception as e:
                logger.error(f"Error initializing OpenAI client: {str(e)}")
    
    def query(self, prompt: str, system_prompt: str = None, model: str = None) -> Dict[str, Any]:
        """
        Query the OpenAI API with a prompt.
        
        Args:
            prompt: The user prompt to send to the API
            system_prompt: Optional system prompt to set context
            model: Model to use (defaults to gpt-4o)
            
        Returns:
            Dict containing the API response
        """
        if not self.client:
            logger.error("OpenAI client not initialized")
            return {"error": "Client not initialized"}
        
        if not system_prompt:
            system_prompt = "You are a helpful assistant that provides accurate information."
        
        try:
            completion = self.client.chat.completions.create(
                model=model or self.default_model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": prompt}
                ],
                response_format={"type": "json_object"}  # Enforce JSON response
            )
            
            return {"choices": [{"message": {"content": completion.choices[0].message.content}}]}
            
        except Exception as e:
            logger.error(f"Error calling OpenAI API: {str(e)}")
            return {"error": f"Error calling API: {str(e)}"}
    
    def extract_json_from_response(self, response: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Extract JSON data from an OpenAI API response.
        
        Args:
            response: The API response dictionary
            
        Returns:
            Extracted JSON data or None if extraction fails
        """
        if "error" in response:
            return None
            
        try:
            content = response["choices"][0]["message"]["content"]
            return json.loads(content)
                
        except (KeyError, json.JSONDecodeError) as e:
            logger.error(f"Failed to extract JSON from response: {str(e)}")
            return None
    
    def get_chemical_info(self, chemical_name: str, chemical_application: str) -> Dict[str, Any]:
        """
        Get structured information about a chemical product.
        
        Args:
            chemical_name: The name of the chemical
            chemical_application: The application of the chemical
            
        Returns:
            Dictionary with chemical information
        """
        prompt = f"Provide information about {chemical_name} in the context of {chemical_application}"
        
        system_prompt = """You are a helpful assistant specializing in chemical information. 
        When provided with a chemical name, and its application, return a JSON object with the product_name, 
        product_family (chemical family/category), cas_number (CAS Registry Number), hs_code (8-digit hs code), hts_number (10-digit HTS code) 
        and product_application (other common uses).
        In case of multiple hs codes, return only the most specific one.
        Format your response as valid JSON only with no additional text."""
        
        response = self.query(prompt, system_prompt)
        json_data = self.extract_json_from_response(response)
        
        if not json_data:
            return {
                "product_name": chemical_name,
                "product_family": "Error",
                "cas_number": "Error",
                "hs_code": "Error",
                "hts_number": "Error",
                "product_application": chemical_application
            }
                
        return json_data
