import logging
import time
import psycopg2
from typing import Dict, Any, List, Optional, Tuple

logger = logging.getLogger(__name__)

class DatabaseClient:
    """
    Client for interacting with the PostgreSQL database.
    """
    
    def __init__(self, db_config: Dict[str, Any]):
        """
        Initialize the database client with configuration.
        
        Args:
            db_config: Dictionary with database connection parameters
        """
        self.db_config = db_config
    
    def get_connection(self):
        """
        Get a database connection.
        
        Returns:
            Database connection object
        """
        try:
            conn = psycopg2.connect(**self.db_config)
            return conn
        except Exception as e:
            logger.error(f"Error connecting to database: {e}")
            raise
    
    def execute_query(self, query: str, params: Optional[Tuple] = None) -> Dict[str, Any]:
        """
        Execute a query against the PostgreSQL database.
        
        Args:
            query: SQL query to execute
            params: Parameters for the query
            
        Returns:
            Dict with query results and metadata
        """
        connection = None
        cursor = None
        
        try:
            # Get a connection
            connection = self.get_connection()
            cursor = connection.cursor()
            
            # Execute query with timing
            start_time = time.time()
            if params:
                logger.info(cursor.mogrify(query, params).decode('utf-8'))
                cursor.execute(query, params)
            else:
                logger.info(query)
                cursor.execute(query)
                
            query_time = time.time() - start_time
            
            # Get column names and results
            column_names = [desc[0] for desc in cursor.description] if cursor.description else []
            results = cursor.fetchall() if cursor.description else []
            
            # Format results as a list of dictionaries
            formatted_results = []
            for row in results:
                formatted_results.append(dict(zip(column_names, row)))
            
            return {
                "metadata": {
                    "row_count": len(formatted_results),
                    "column_count": len(column_names),
                    "columns": column_names,
                    "query_time_ms": round(query_time * 1000, 2)
                },
                "data": formatted_results
            }
            
        except Exception as e:
            logger.error(f"Database error: {e}")
            if connection:
                connection.rollback()
            raise Exception(f"Database error: {e}")
            
        finally:
            # Close resources
            if cursor:
                cursor.close()
            if connection:
                connection.close()
    
    def health_check(self) -> bool:
        """
        Perform a health check on the database connection.
        
        Returns:
            True if the database is healthy, False otherwise
        """
        try:
            connection = self.get_connection()
            cursor = connection.cursor()
            cursor.execute("SELECT 1")
            cursor.close()
            connection.close()
            return True
        except Exception as e:
            logger.error(f"Database health check failed: {e}")
            return False
