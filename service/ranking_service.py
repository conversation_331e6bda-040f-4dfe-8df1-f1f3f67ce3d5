import logging
from typing import Dict, Any, List

logger = logging.getLogger(__name__)

class RankingService:
    """
    Service for ranking suppliers and countries.
    """
    
    def __init__(self):
        """
        Initialize the ranking service.
        """
        pass
    
    def _normalize_values(self, values: List[float], higher_is_better: bool = True) -> List[float]:
        """
        Normalize a list of values to a 0-1 scale.
        
        Args:
            values: List of values to normalize
            higher_is_better: Whether higher values are better
            
        Returns:
            List of normalized values
        """
        min_val = min(values)
        max_val = max(values)
        
        # Handle the case where all values are the same
        if min_val == max_val:
            return [1.0 if higher_is_better else 0.0] * len(values)
        
        if higher_is_better:
            return [(val - min_val) / (max_val - min_val) for val in values]
        else:
            return [1 - ((val - min_val) / (max_val - min_val)) for val in values]
    
    def _calculate_supplier_scores(self, suppliers: List[Dict[str, Any]]) -> List[float]:
        """
        Calculate composite scores for each supplier using equal weighting.
        
        Args:
            suppliers: List of supplier dictionaries
            
        Returns:
            List of scores for each supplier
        """
        # Calculate unit costs and price consistency
        unit_costs = []
        price_consistency = []
        shipment_counts = []
        total_quantities = []
        
        for supplier in suppliers:
            # Handle edge case: if average_quantity_per_shipment is 0
            avg_quantity = supplier.get("average_quantity_per_shipment", 0)
            if avg_quantity == 0:
                unit_cost = float('inf')  # Assign a very high unit cost
            else:
                unit_cost = supplier.get("avg_price_per_ton", 0) / avg_quantity
            
            # Handle edge case: if average_fob is 0
            avg_fob = supplier.get("avg_price_per_ton", 0)
            if avg_fob == 0:
                consistency = 0  # Assign 0 consistency when average FOB is 0
            else:
                # Use minimum_fob if available, otherwise use avg_fob * 0.8 as an estimate
                min_fob = supplier.get("minimum_fob", avg_fob * 0.8)
                consistency = min_fob / avg_fob
            
            unit_costs.append(unit_cost)
            price_consistency.append(consistency)
            shipment_counts.append(supplier.get("transaction_count", 0))
            total_quantities.append(supplier.get("export_volume_tons", 0))
        
        # Normalize all values to 0-1 scale
        norm_shipment_counts = self._normalize_values(shipment_counts, higher_is_better=True)
        norm_total_quantities = self._normalize_values(total_quantities, higher_is_better=True)
        norm_unit_costs = self._normalize_values(unit_costs, higher_is_better=False)
        norm_price_consistency = self._normalize_values(price_consistency, higher_is_better=True)
        
        # Calculate composite scores with equal weights (0.25 each)
        scores = []
        for i in range(len(suppliers)):
            score = (
                0.25 * norm_shipment_counts[i] +
                0.25 * norm_total_quantities[i] +
                0.25 * norm_unit_costs[i] +
                0.25 * norm_price_consistency[i]
            )
            scores.append(score)
        
        return scores
    
    def rank_suppliers(self, suppliers: List[Dict[str, Any]], top_n: int = 3) -> List[Dict[str, Any]]:
        """
        Rank suppliers based on composite scores and return top N with rank and score.
        
        Args:
            suppliers: List of supplier dictionaries
            top_n: Number of top suppliers to return
            
        Returns:
            List of ranked supplier dictionaries
        """
        if not suppliers:
            return []
            
        # Calculate scores for all suppliers
        scores = self._calculate_supplier_scores(suppliers)
        
        # Create a list of (supplier, score) tuples
        supplier_scores = list(zip(suppliers, scores))
        
        # Sort by score in descending order
        sorted_suppliers = sorted(supplier_scores, key=lambda x: x[1], reverse=True)
        
        # If N or fewer suppliers, include all with ranks
        if len(suppliers) <= top_n:
            ranked_suppliers = []
            for i, (supplier, score) in enumerate(sorted_suppliers):
                # Create a copy of the supplier dict to avoid modifying the original
                supplier_with_rank = supplier.copy()
                # Add rank (1-based) and score
                supplier_with_rank["Rank"] = i + 1
                supplier_with_rank["Score"] = round(score, 4)  # Round to 4 decimal places for readability
                ranked_suppliers.append(supplier_with_rank)
            return ranked_suppliers
        
        # Otherwise, include only top N with ranks
        ranked_suppliers = []
        for i, (supplier, score) in enumerate(sorted_suppliers[:top_n]):
            # Create a copy of the supplier dict to avoid modifying the original
            supplier_with_rank = supplier.copy()
            # Add rank (1-based) and score
            supplier_with_rank["Rank"] = i + 1
            supplier_with_rank["Score"] = round(score, 4)  # Round to 4 decimal places for readability
            ranked_suppliers.append(supplier_with_rank)
        
        return ranked_suppliers
    
    def _calculate_country_scores(self, countries: List[Dict[str, Any]]) -> List[float]:
        """
        Calculate composite scores for countries based on average FOB, shipment count, and duty.
        
        Args:
            countries: List of country dictionaries
            
        Returns:
            List of scores for each country
        """
        # Extract values for normalization
        avg_fobs = [country.get('avg_price_per_ton', 0) for country in countries]
        shipment_counts = [country.get('transaction_count', 0) for country in countries]
        duty_percentages = [country.get('duty_percentage', 0) for country in countries]
        
        # Normalize values
        norm_avg_fobs = self._normalize_values(avg_fobs, higher_is_better=True)
        norm_shipment_counts = self._normalize_values(shipment_counts, higher_is_better=True)
        norm_duty_percentages = self._normalize_values(duty_percentages, higher_is_better=False)
        
        # Calculate composite scores with weights (higher weight for duty)
        # Weight distribution: 20% FOB, 20% shipment count, 60% duty
        scores = []
        for i in range(len(countries)):
            score = (
                0.20 * norm_avg_fobs[i] +
                0.20 * norm_shipment_counts[i] +
                0.60 * norm_duty_percentages[i]
            )
            scores.append(score)
        
        return scores
    
    def rank_countries(self, countries: List[Dict[str, Any]], top_n: int = 3) -> List[Dict[str, Any]]:
        """
        Rank countries based on composite scores and return top N with rank and score.
        
        Args:
            countries: List of country dictionaries
            top_n: Number of top countries to return
            
        Returns:
            List of ranked country dictionaries
        """
        if not countries:
            return []
            
        # First, ensure each country has all required fields
        for country in countries:
            if 'duty_percentage' not in country:
                logger.warning(f"Missing duty_percentage for country: {country.get('country')}")
                country['duty_percentage'] = 0.0
        
        # Calculate scores
        scores = self._calculate_country_scores(countries)
        
        # Create a list of (country, score) tuples
        country_scores = list(zip(countries, scores))
        
        # Sort by score in descending order
        sorted_countries = sorted(country_scores, key=lambda x: x[1], reverse=True)
        
        # Take top N (or all if fewer than N)
        top_count = min(top_n, len(sorted_countries))
        ranked_countries = []
        
        for i in range(top_count):
            country, score = sorted_countries[i]
            # Create a copy to avoid modifying the original
            ranked_country = country.copy()
            # Add rank and score
            ranked_country['Rank'] = i + 1
            ranked_country['Score'] = round(score, 4)
            ranked_countries.append(ranked_country)
        
        return ranked_countries
