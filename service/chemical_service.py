import os
import re
import json
import pandas as pd
import logging
from typing import Dict, Any, List, Optional, Tuple

from model.chemical import Chemical, ChemicalLookupResult
from service.client.llm_client import LLMClient

logger = logging.getLogger(__name__)

class ChemicalService:
    """
    Service for chemical-related operations.
    """

    def __init__(self, llm_client: LLMClient, historical_data_path: str = None):
        """
        Initialize the chemical service.

        Args:
            llm_client: LLM client for chemical information
            historical_data_path: Path to historical data CSV file
        """
        self.llm_client = llm_client
        self.historical_data_path = historical_data_path
        self.historical_df = self._load_historical_data()

        # Files for storing results
        self.raw_output_file = "raw_output.jsonl"
        self.results_file = "results.jsonl"

    def _load_historical_data(self) -> pd.DataFrame:
        """
        Load historical chemical data from CSV.

        Returns:
            DataFrame with historical data
        """
        if not self.historical_data_path or not os.path.exists(self.historical_data_path):
            logger.warning(f"Historical data file not found at {self.historical_data_path}")
            return pd.DataFrame(columns=['product_name', 'hs_code'])

        try:
            logger.info(f"Loading historical data from {self.historical_data_path}")
            df = pd.read_csv(self.historical_data_path)

            # Select only necessary columns
            if 'product_name' in df.columns and 'hs_code' in df.columns:
                df = df[['product_name', 'hs_code']]
                df = df.drop_duplicates(subset=['product_name', 'hs_code'])
                df.dropna(inplace=True)
                df.reset_index(drop=True, inplace=True)
                logger.info(f"Loaded historical data with {len(df.index)} records")
            else:
                logger.warning(f"CSV file does not contain required columns. Expected 'product_name' and 'hs_code'.")
                df = pd.DataFrame(columns=['product_name', 'hs_code'])

            return df
        except Exception as e:
            logger.error(f"Error loading historical data: {str(e)}")
            return pd.DataFrame(columns=['product_name', 'hs_code'])

    def _normalize_hs_code(self, hs_code: str) -> str:
        """
        Normalize HS code by removing non-digit characters.

        Args:
            hs_code: HS code string which may contain formatting characters

        Returns:
            Normalized HS code with only digits
        """
        if not hs_code:
            return ""

        # Remove all non-digit characters
        return re.sub(r'[^0-9]', '', hs_code)

    def _normalize_cas_number(self, cas_number: str) -> str:
        """
        Normalize CAS number by removing non-digit characters.

        Args:
            cas_number: CAS number string which may contain hyphens or spaces

        Returns:
            Normalized CAS number with only digits
        """
        if not cas_number:
            return ""

        # Remove all non-digit characters
        return re.sub(r'[^0-9]', '', cas_number)

    def _perform_hard_match(self, json1: Dict[str, Any], json2: Dict[str, Any]) -> Dict[str, bool]:
        """
        Perform hard matching on key fields: hs_code and cas_number.

        Args:
            json1: First JSON response with chemical information
            json2: Second JSON response with chemical information

        Returns:
            Dictionary with match results for each field and an overall match indicator
        """
        results = {
            "hs_code_match": False,
            "cas_number_match": False,
            "overall_match": False
        }

        # Check hs_code match
        hs_code1 = self._normalize_hs_code(json1.get("hs_code", ""))
        hs_code2 = self._normalize_hs_code(json2.get("hs_code", ""))

        results["hs_code_match"] = (hs_code1 == hs_code2) and (hs_code1 != "")

        # Check cas_number match
        cas1 = self._normalize_cas_number(json1.get("cas_number", ""))
        cas2 = self._normalize_cas_number(json2.get("cas_number", ""))

        # If they're not blank and equal then true, if one is blank and other isn't then also true.
        results["cas_number_match"] = ((cas1 == cas2) and (cas1 != "")) or ((cas1 == "") != (cas2 == ""))

        results["overall_match"] = results["cas_number_match"]

        return results

    def _compute_catalog(self, chemical_name: str, chemical_application: str) -> Dict[str, Any]:
        """
        Compute chemical information using multiple sources and compare results.

        Args:
            chemical_name: Name of the chemical to look up
            chemical_application: Application of the chemical

        Returns:
            dict: A dictionary containing the chemical information and HS code
        """
        try:
            try:
                # Query chatGPT
                result_cgpt = self.llm_client.get_chemical_info(chemical_name, chemical_application)
                # Query Perplexity
                result_p = self.llm_client.query_perplexity(chemical_name, chemical_application)

                # compute hard match
                match_results = self._perform_hard_match(result_cgpt, result_p)
            except Exception as e:
                logger.error(f"Error querying LLM APIs: {str(e)}")
                return {
                    "hs_code": "",
                    "error": f"Error querying LLM APIs: {str(e)}"
                }

            raw_output = {
                chemical_name: {
                    "AI Responses": {
                        "ChatGPT": result_cgpt,
                        "Perplexity": result_p
                    },
                    "Match Results": match_results
                }
            }

            logger.info(f"Match results for {chemical_name}:")
            for field, result in match_results.items():
                logger.info(f"{field}: {result}")

            if match_results["overall_match"]:
                logger.info("The two chemical records are considered to be the same product.")
                # Append the correct and available cas_number to the output
                # Only in the case where cgpt is blank and p isn't then add p to cgpt.
                cas_p = result_p.get('cas_number', '')
                cas_cgpt = result_cgpt.get('cas_number', '')
                if cas_cgpt is None and cas_p is not None:
                    result_cgpt['cas_number'] = cas_p

                # Append the hs codes together if they are different
                if not match_results["hs_code_match"]:
                    # Handle the case where one or both HS codes might be None
                    hs_cgpt = result_cgpt.get('hs_code', '')
                    hs_p = result_p.get('hs_code', '')

                    # Convert None values to empty strings
                    hs_cgpt = '' if hs_cgpt is None else str(hs_cgpt).strip()
                    hs_p = '' if hs_p is None else str(hs_p).strip()

                    # Define patterns for non-useful HS codes
                    non_useful_patterns = ['not found', 'n/a', 'na', 'none', 'error', 'unknown', 'not available']

                    # Only append valid HS codes (digits, dots, spaces)
                    combined_hs = []
                    if hs_cgpt and not any(pattern in hs_cgpt.lower() for pattern in non_useful_patterns):
                        # Check if it contains at least some digits (likely a valid HS code)
                        if re.search(r'\d', hs_cgpt):
                            combined_hs.append(hs_cgpt)

                    if hs_p and not any(pattern in hs_p.lower() for pattern in non_useful_patterns):
                        # Check if it contains at least some digits (likely a valid HS code)
                        if re.search(r'\d', hs_p) and hs_p not in combined_hs:  # Avoid duplication
                            combined_hs.append(hs_p)

                    # Join the values or default to empty string
                    result_cgpt['hs_code'] = " ".join(combined_hs) if combined_hs else ""

                # Add back the original chemical application as a category key to output
                result_cgpt['mstack_category'] = chemical_application
                answer = result_cgpt.get('hs_code', '')
            else:
                logger.info("The two chemical records appear to be different products. The LLMs disagree on the CAS numbers hence returning none")
                answer = ""

            # Add the results to json files for audit
            with open(self.raw_output_file, 'a', encoding='utf-8') as raw_file:
                raw_file.write(json.dumps(raw_output) + '\n')

            with open(self.results_file, 'a', encoding='utf-8') as results_file:
                results_file.write(json.dumps(result_cgpt) + '\n')

            # Return HS Code
            return {
                "hs_code": answer if isinstance(answer, str) else ""
            }
        except Exception as e:
            logger.error(f"Error processing chemical {chemical_name}: {str(e)}")
            error_output = {chemical_name: {"Error": str(e)}}
            with open(self.raw_output_file, 'a', encoding='utf-8') as raw_file:
                raw_file.write(json.dumps(error_output) + '\n')
            return {
                "hs_code": "",
                "error": f"Error processing chemical: {str(e)}"
            }

    def lookup_chemical(self, chemical_name: str, chemical_application: str) -> ChemicalLookupResult:
        """
        Look up chemical information either from historical data or by querying APIs.

        Args:
            chemical_name: Name of the chemical to look up
            chemical_application: Application of the chemical

        Returns:
            ChemicalLookupResult with status and data
        """
        # Check if chemical exists in historical data
        matching_rows = self.historical_df.loc[self.historical_df['product_name'] == chemical_name]

        if not matching_rows.empty:
            logger.info(f"Found {chemical_name} in historical data")
            hs_code = matching_rows['hs_code'].values[0]

            # Return success result with HS code
            return ChemicalLookupResult(
                status="success",
                data=[{
                    "hs_code": hs_code,
                    "product_family": [chemical_name],
                    "description": f"{chemical_name} - {chemical_application or 'General'}"
                }]
            )
        else:
            # Call the compute catalog function
            logger.info(f"Querying APIs for {chemical_name}...")
            result = self._compute_catalog(chemical_name, chemical_application)

            if "error" in result:
                return ChemicalLookupResult(
                    status="error",
                    message=result["error"]
                )
            elif result["hs_code"]:
                return ChemicalLookupResult(
                    status="success",
                    data=[{
                        "hs_code": result["hs_code"],
                        "product_family": [chemical_name],
                        "description": f"{chemical_name} - {chemical_application or 'General'}"
                    }]
                )
            else:
                return ChemicalLookupResult(
                    status="error",
                    message="Could not determine HS code for the chemical"
                )
