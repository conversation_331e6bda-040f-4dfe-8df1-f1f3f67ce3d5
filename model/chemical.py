from dataclasses import dataclass
from typing import List, Optional, Dict, Any


@dataclass
class Chemical:
    """
    Represents a chemical product with its properties and HS code.
    """
    name: str
    application: str
    hs_code: Optional[str] = None
    product_family: Optional[str] = None
    cas_number: Optional[str] = None
    description: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert the chemical object to a dictionary."""
        return {
            "chemical_name": self.name,
            "chemical_application": self.application,
            "hs_code": self.hs_code,
            "product_family": self.product_family,
            "cas_number": self.cas_number,
            "description": self.description
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Chemical':
        """Create a Chemical object from a dictionary."""
        return cls(
            name=data.get("chemical_name", "") or data.get("product_name", ""),
            application=data.get("chemical_application", "") or data.get("product_application", ""),
            hs_code=data.get("hs_code"),
            product_family=data.get("product_family"),
            cas_number=data.get("cas_number"),
            description=data.get("description")
        )


@dataclass
class ChemicalLookupResult:
    """
    Represents the result of a chemical lookup operation.
    """
    status: str  # "success", "error", "warning"
    message: Optional[str] = None
    data: Optional[List[Dict[str, Any]]] = None
